2025-05-30 00:46:35,790 - web_dashboard - INFO - Starting dashboard on http://127.0.0.1:5000
2025-05-30 00:46:35,795 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-30 00:46:35,796 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-30 00:46:37,795 - trading_bot - INFO - Trading Bot initialized
2025-05-30 00:46:37,796 - trading_bot - INFO - Dry run mode: False
2025-05-30 00:46:37,796 - trading_bot - INFO - Trading pair: XBTMYR
2025-05-30 00:46:37,796 - trading_bot - INFO - Starting Trading Bot...
2025-05-30 00:46:38,073 - luno_client - ERROR - API request failed: 401 Client Error: Unauthorized for url: https://api.luno.com/api/1/balance
2025-05-30 00:46:38,074 - luno_client - ERROR - DEBUG: Request URL: https://api.luno.com/api/1/balance
2025-05-30 00:46:38,074 - luno_client - ERROR - DEBUG: Request params: None
2025-05-30 00:46:38,074 - luno_client - ERROR - DEBUG: Response status: 401
2025-05-30 00:46:38,074 - luno_client - ERROR - DEBUG: Response headers: {'Date': 'Thu, 29 May 2025 16:46:38 GMT', 'Content-Type': 'text/plain; charset=utf-8', 'Content-Length': '13', 'Connection': 'keep-alive', 'X-Content-Type-Options': 'nosniff', 'X-Luno-Trace-Id': '57df1c43ea38e77078b2f575edf9d294', 'cf-cache-status': 'DYNAMIC', 'Strict-Transport-Security': 'max-age=60', 'Server': 'cloudflare', 'CF-RAY': '9477780ecc414b86-SIN'}
2025-05-30 00:46:38,074 - luno_client - ERROR - DEBUG: Response body: Unauthorized

2025-05-30 00:46:38,074 - luno_client - ERROR - Failed to update balances: Luno API error: 401 Client Error: Unauthorized for url: https://api.luno.com/api/1/balance
2025-05-30 00:46:38,322 - luno_client - ERROR - API request failed: 401 Client Error: Unauthorized for url: https://api.luno.com/api/1/listorders?state=PENDING
2025-05-30 00:46:38,322 - luno_client - ERROR - DEBUG: Request URL: https://api.luno.com/api/1/listorders
2025-05-30 00:46:38,322 - luno_client - ERROR - DEBUG: Request params: {'state': 'PENDING'}
2025-05-30 00:46:38,322 - luno_client - ERROR - DEBUG: Response status: 401
2025-05-30 00:46:38,322 - luno_client - ERROR - DEBUG: Response headers: {'Date': 'Thu, 29 May 2025 16:46:38 GMT', 'Content-Type': 'text/plain; charset=utf-8', 'Content-Length': '13', 'Connection': 'keep-alive', 'X-Content-Type-Options': 'nosniff', 'X-Luno-Trace-Id': '565371bd989360b05b3ef7ec183036ef', 'cf-cache-status': 'DYNAMIC', 'Strict-Transport-Security': 'max-age=60', 'Server': 'cloudflare', 'CF-RAY': '947778101cf64b86-SIN'}
2025-05-30 00:46:38,322 - luno_client - ERROR - DEBUG: Response body: Unauthorized

2025-05-30 00:46:38,322 - luno_client - ERROR - Failed to update open orders: Luno API error: 401 Client Error: Unauthorized for url: https://api.luno.com/api/1/listorders?state=PENDING
2025-05-30 00:46:38,322 - trading_bot - INFO - === Trading Bot Initial State ===
2025-05-30 00:46:38,322 - trading_bot - INFO - Config: XBTMYR
2025-05-30 00:46:38,322 - trading_bot - INFO - Max position size: 2.0%
2025-05-30 00:46:38,322 - trading_bot - INFO - Stop loss: 1.5%
2025-05-30 00:46:38,322 - trading_bot - INFO - Take profit: 3.0%
2025-05-30 00:46:38,322 - trading_bot - INFO - Portfolio value: {'base_currency_amount': 0, 'base_currency_value': 0, 'counter_currency_amount': 0, 'total_value': 0, 'current_price': 460000}
2025-05-30 00:46:42,974 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 00:46:42] "GET /api/bot_status HTTP/1.1" 200 -
2025-05-30 00:46:42,975 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 00:46:42] "GET /api/trades HTTP/1.1" 200 -
2025-05-30 00:46:43,312 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 00:46:43] "GET /api/market_data HTTP/1.1" 200 -
2025-05-30 00:46:43,825 - luno_client - ERROR - API request failed: 401 Client Error: Unauthorized for url: https://api.luno.com/api/1/balance
2025-05-30 00:46:43,826 - luno_client - ERROR - DEBUG: Request URL: https://api.luno.com/api/1/balance
2025-05-30 00:46:43,826 - luno_client - ERROR - DEBUG: Request params: None
2025-05-30 00:46:43,826 - luno_client - ERROR - DEBUG: Response status: 401
2025-05-30 00:46:43,826 - luno_client - ERROR - DEBUG: Response headers: {'Date': 'Thu, 29 May 2025 16:46:43 GMT', 'Content-Type': 'text/plain; charset=utf-8', 'Content-Length': '13', 'Connection': 'keep-alive', 'X-Content-Type-Options': 'nosniff', 'X-Luno-Trace-Id': '137eece3dde0efcc56e7c902e18aebb3', 'cf-cache-status': 'DYNAMIC', 'Strict-Transport-Security': 'max-age=60', 'Server': 'cloudflare', 'CF-RAY': '9477782f1a55a8eb-SIN'}
2025-05-30 00:46:43,827 - luno_client - ERROR - DEBUG: Response body: Unauthorized

2025-05-30 00:46:43,827 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 00:46:43] "GET /api/portfolio HTTP/1.1" 200 -
2025-05-30 00:46:48,226 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 00:46:48,227 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:46:48,227 - trading_bot - INFO - Cancelled 0 open orders
2025-05-30 00:46:48,227 - trading_bot - INFO - Performance report saved: trading_report_20250530_004648.json
2025-05-30 00:46:48,228 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:47:00,758 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 00:47:00] "GET /api/bot_status HTTP/1.1" 200 -
2025-05-30 00:47:00,761 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 00:47:00] "GET /api/trades HTTP/1.1" 200 -
2025-05-30 00:47:00,961 - luno_client - ERROR - API request failed: 401 Client Error: Unauthorized for url: https://api.luno.com/api/1/balance
2025-05-30 00:47:00,961 - luno_client - ERROR - DEBUG: Request URL: https://api.luno.com/api/1/balance
2025-05-30 00:47:00,962 - luno_client - ERROR - DEBUG: Request params: None
2025-05-30 00:47:00,962 - luno_client - ERROR - DEBUG: Response status: 401
2025-05-30 00:47:00,962 - luno_client - ERROR - DEBUG: Response headers: {'Date': 'Thu, 29 May 2025 16:47:00 GMT', 'Content-Type': 'text/plain; charset=utf-8', 'Content-Length': '13', 'Connection': 'keep-alive', 'X-Content-Type-Options': 'nosniff', 'X-Luno-Trace-Id': '03d8bbb11888e3426f725ab58ba689dd', 'cf-cache-status': 'DYNAMIC', 'Strict-Transport-Security': 'max-age=60', 'Server': 'cloudflare', 'CF-RAY': '9477789de92a6198-SIN'}
2025-05-30 00:47:00,962 - luno_client - ERROR - DEBUG: Response body: Unauthorized

2025-05-30 00:47:00,962 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 00:47:00] "GET /api/portfolio HTTP/1.1" 200 -
2025-05-30 00:47:00,979 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 00:47:00] "GET /api/market_data HTTP/1.1" 200 -
2025-05-30 00:47:16,329 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 00:47:16,329 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:47:16,329 - trading_bot - INFO - Cancelled 0 open orders
2025-05-30 00:47:16,330 - trading_bot - INFO - Performance report saved: trading_report_20250530_004716.json
2025-05-30 00:47:16,330 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:47:19,115 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 00:47:19,116 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:47:19,116 - trading_bot - INFO - Cancelled 0 open orders
2025-05-30 00:47:19,117 - trading_bot - INFO - Performance report saved: trading_report_20250530_004719.json
2025-05-30 00:47:19,117 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:47:19,622 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 00:47:19,623 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:47:19,623 - trading_bot - INFO - Cancelled 0 open orders
2025-05-30 00:47:19,623 - trading_bot - INFO - Performance report saved: trading_report_20250530_004719.json
2025-05-30 00:47:19,623 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:47:19,796 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 00:47:19,796 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:47:19,796 - trading_bot - INFO - Cancelled 0 open orders
2025-05-30 00:47:19,797 - trading_bot - INFO - Performance report saved: trading_report_20250530_004719.json
2025-05-30 00:47:19,797 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:47:19,954 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 00:47:19,954 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:47:19,955 - trading_bot - INFO - Cancelled 0 open orders
2025-05-30 00:47:19,955 - trading_bot - INFO - Performance report saved: trading_report_20250530_004719.json
2025-05-30 00:47:19,955 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:47:20,279 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 00:47:20,280 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:47:20,280 - trading_bot - INFO - Cancelled 0 open orders
2025-05-30 00:47:20,281 - trading_bot - INFO - Performance report saved: trading_report_20250530_004720.json
2025-05-30 00:47:20,281 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:47:20,443 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 00:47:20,444 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:47:20,444 - trading_bot - INFO - Cancelled 0 open orders
2025-05-30 00:47:20,444 - trading_bot - INFO - Performance report saved: trading_report_20250530_004720.json
2025-05-30 00:47:20,444 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:47:20,603 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 00:47:20,604 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:47:20,604 - trading_bot - INFO - Cancelled 0 open orders
2025-05-30 00:47:20,604 - trading_bot - INFO - Performance report saved: trading_report_20250530_004720.json
2025-05-30 00:47:20,604 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:47:20,772 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 00:47:20,772 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:47:20,772 - trading_bot - INFO - Cancelled 0 open orders
2025-05-30 00:47:20,773 - trading_bot - INFO - Performance report saved: trading_report_20250530_004720.json
2025-05-30 00:47:20,773 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:47:20,955 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 00:47:20,956 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:47:20,956 - trading_bot - INFO - Cancelled 0 open orders
2025-05-30 00:47:20,956 - trading_bot - INFO - Performance report saved: trading_report_20250530_004720.json
2025-05-30 00:47:20,956 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:47:21,113 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 00:47:21,113 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:47:21,113 - trading_bot - INFO - Cancelled 0 open orders
2025-05-30 00:47:21,114 - trading_bot - INFO - Performance report saved: trading_report_20250530_004721.json
2025-05-30 00:47:21,114 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:47:21,320 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 00:47:21,320 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:47:21,320 - trading_bot - INFO - Cancelled 0 open orders
2025-05-30 00:47:21,321 - trading_bot - INFO - Performance report saved: trading_report_20250530_004721.json
2025-05-30 00:47:21,321 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:47:30,760 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 00:47:30] "GET /api/bot_status HTTP/1.1" 200 -
2025-05-30 00:47:30,765 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 00:47:30] "GET /api/trades HTTP/1.1" 200 -
2025-05-30 00:47:31,031 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 00:47:31] "GET /api/market_data HTTP/1.1" 200 -
2025-05-30 00:47:31,031 - luno_client - ERROR - API request failed: 401 Client Error: Unauthorized for url: https://api.luno.com/api/1/balance
2025-05-30 00:47:31,031 - luno_client - ERROR - DEBUG: Request URL: https://api.luno.com/api/1/balance
2025-05-30 00:47:31,032 - luno_client - ERROR - DEBUG: Request params: None
2025-05-30 00:47:31,032 - luno_client - ERROR - DEBUG: Response status: 401
2025-05-30 00:47:31,032 - luno_client - ERROR - DEBUG: Response headers: {'Date': 'Thu, 29 May 2025 16:47:31 GMT', 'Content-Type': 'text/plain; charset=utf-8', 'Content-Length': '13', 'Connection': 'keep-alive', 'X-Content-Type-Options': 'nosniff', 'X-Luno-Trace-Id': '748807c70d225bb63ac3dc18c426ba46', 'cf-cache-status': 'DYNAMIC', 'Strict-Transport-Security': 'max-age=60', 'Server': 'cloudflare', 'CF-RAY': '947779596989a8eb-SIN'}
2025-05-30 00:47:31,032 - luno_client - ERROR - DEBUG: Response body: Unauthorized

2025-05-30 00:47:31,032 - werkzeug - INFO - 127.0.0.1 - - [30/May/2025 00:47:31] "GET /api/portfolio HTTP/1.1" 200 -
2025-05-30 00:48:26,635 - luno_client - INFO - DEBUG: LunoAPIClient initialized
2025-05-30 00:48:26,635 - luno_client - INFO - DEBUG: API key provided: Yes
2025-05-30 00:48:26,635 - luno_client - INFO - DEBUG: API secret provided: Yes
2025-05-30 00:48:26,635 - luno_client - INFO - DEBUG: API key length: 13
2025-05-30 00:48:26,635 - luno_client - INFO - DEBUG: API secret length: 43
2025-05-30 00:48:26,635 - trading_bot - INFO - Trading Bot initialized
2025-05-30 00:48:26,635 - trading_bot - INFO - Dry run mode: True
2025-05-30 00:48:26,635 - trading_bot - INFO - Trading pair: XBTMYR
2025-05-30 00:48:26,635 - trading_bot - INFO - Starting Trading Bot...
2025-05-30 00:48:26,635 - trading_bot - INFO - === Trading Bot Initial State ===
2025-05-30 00:48:26,635 - trading_bot - INFO - Config: XBTMYR
2025-05-30 00:48:26,635 - trading_bot - INFO - Max position size: 2.0%
2025-05-30 00:48:26,635 - trading_bot - INFO - Stop loss: 1.5%
2025-05-30 00:48:26,635 - trading_bot - INFO - Take profit: 3.0%
2025-05-30 00:51:29,876 - luno_client - INFO - DEBUG: LunoAPIClient initialized
2025-05-30 00:51:29,877 - luno_client - INFO - DEBUG: API key provided: Yes
2025-05-30 00:51:29,877 - luno_client - INFO - DEBUG: API secret provided: Yes
2025-05-30 00:51:29,877 - luno_client - INFO - DEBUG: API key length: 13
2025-05-30 00:51:29,877 - luno_client - INFO - DEBUG: API secret length: 43
2025-05-30 00:51:29,877 - trading_bot - INFO - Trading Bot initialized
2025-05-30 00:51:29,877 - trading_bot - INFO - Dry run mode: True
2025-05-30 00:51:29,877 - trading_bot - INFO - Trading pair: XBTMYR
2025-05-30 00:51:29,877 - trading_bot - INFO - Starting Trading Bot...
2025-05-30 00:51:29,877 - trading_bot - INFO - === Trading Bot Initial State ===
2025-05-30 00:51:29,877 - trading_bot - INFO - Config: XBTMYR
2025-05-30 00:51:29,877 - trading_bot - INFO - Max position size: 2.0%
2025-05-30 00:51:29,877 - trading_bot - INFO - Stop loss: 1.5%
2025-05-30 00:51:29,877 - trading_bot - INFO - Take profit: 3.0%
2025-05-30 00:54:09,853 - trading_bot - INFO - Received signal 15, shutting down gracefully...
2025-05-30 00:54:09,853 - trading_bot - INFO - Received signal 15, shutting down gracefully...
2025-05-30 00:54:09,854 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:54:09,854 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:54:09,854 - trading_bot - INFO - Performance report saved: trading_report_20250530_005409.json
2025-05-30 00:54:09,854 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:54:09,854 - trading_bot - INFO - Performance report saved: trading_report_20250530_005409.json
2025-05-30 00:54:09,854 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:54:36,987 - luno_client - INFO - DEBUG: LunoAPIClient initialized
2025-05-30 00:54:36,987 - luno_client - INFO - DEBUG: API key provided: Yes
2025-05-30 00:54:36,987 - luno_client - INFO - DEBUG: API secret provided: Yes
2025-05-30 00:54:36,987 - luno_client - INFO - DEBUG: API key length: 13
2025-05-30 00:54:36,987 - luno_client - INFO - DEBUG: API secret length: 43
2025-05-30 00:54:36,987 - trading_bot - INFO - Trading Bot initialized
2025-05-30 00:54:36,987 - trading_bot - INFO - Dry run mode: True
2025-05-30 00:54:36,987 - trading_bot - INFO - Trading pair: XBTMYR
2025-05-30 00:54:36,987 - trading_bot - INFO - Starting Trading Bot...
2025-05-30 00:54:36,987 - trading_bot - INFO - === Trading Bot Initial State ===
2025-05-30 00:54:36,987 - trading_bot - INFO - Config: XBTMYR
2025-05-30 00:54:36,987 - trading_bot - INFO - Max position size: 2.0%
2025-05-30 00:54:36,987 - trading_bot - INFO - Stop loss: 1.5%
2025-05-30 00:54:36,987 - trading_bot - INFO - Take profit: 3.0%
2025-05-30 00:54:44,633 - trading_bot - INFO - Received signal 15, shutting down gracefully...
2025-05-30 00:54:44,633 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:54:44,634 - trading_bot - INFO - Performance report saved: trading_report_20250530_005444.json
2025-05-30 00:54:44,634 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:55:24,566 - luno_client - INFO - LunoAPIClient initialized successfully
2025-05-30 00:55:24,566 - trading_bot - INFO - Trading Bot initialized
2025-05-30 00:55:24,566 - trading_bot - INFO - Dry run mode: True
2025-05-30 00:55:24,566 - trading_bot - INFO - Trading pair: XBTMYR
2025-05-30 00:55:24,566 - trading_bot - INFO - Starting Trading Bot...
2025-05-30 00:55:24,566 - trading_bot - INFO - === Trading Bot Initial State ===
2025-05-30 00:55:24,567 - trading_bot - INFO - Config: XBTMYR
2025-05-30 00:55:24,567 - trading_bot - INFO - Max position size: 2.0%
2025-05-30 00:55:24,567 - trading_bot - INFO - Stop loss: 1.5%
2025-05-30 00:55:24,567 - trading_bot - INFO - Take profit: 3.0%
2025-05-30 00:55:29,235 - trading_bot - INFO - Received signal 15, shutting down gracefully...
2025-05-30 00:55:29,236 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:55:29,236 - trading_bot - INFO - Received signal 15, shutting down gracefully...
2025-05-30 00:55:29,236 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 00:55:29,236 - trading_bot - INFO - Performance report saved: trading_report_20250530_005529.json
2025-05-30 00:55:29,236 - trading_bot - INFO - Trading Bot stopped
2025-05-30 00:55:29,236 - trading_bot - INFO - Performance report saved: trading_report_20250530_005529.json
2025-05-30 00:55:29,236 - trading_bot - INFO - Trading Bot stopped
2025-05-30 07:56:20,346 - luno_client - INFO - DEBUG: LunoAPIClient initialized
2025-05-30 07:56:20,347 - luno_client - INFO - DEBUG: API key provided: Yes
2025-05-30 07:56:20,347 - luno_client - INFO - DEBUG: API secret provided: Yes
2025-05-30 07:56:20,347 - luno_client - INFO - DEBUG: API key length: 9
2025-05-30 07:56:20,347 - luno_client - INFO - DEBUG: API secret length: 12
2025-05-30 07:56:20,347 - luno_client - INFO - LunoAPIClient initialized successfully
2025-05-30 07:56:20,347 - luno_client - INFO - DEBUG: LunoAPIClient initialized
2025-05-30 07:56:20,347 - luno_client - INFO - DEBUG: API key provided: Yes
2025-05-30 07:56:20,347 - luno_client - INFO - DEBUG: API secret provided: Yes
2025-05-30 07:56:20,347 - luno_client - INFO - DEBUG: API key length: 13
2025-05-30 07:56:20,347 - luno_client - INFO - DEBUG: API secret length: 43
2025-05-30 07:56:20,347 - luno_client - INFO - LunoAPIClient initialized successfully
2025-05-30 07:56:20,347 - trading_bot - INFO - Trading Bot initialized
2025-05-30 07:56:20,347 - trading_bot - INFO - Dry run mode: True
2025-05-30 07:56:20,347 - trading_bot - INFO - Trading pair: XBTMYR
2025-05-30 08:01:47,324 - luno_client - INFO - DEBUG: LunoAPIClient initialized
2025-05-30 08:01:47,324 - luno_client - INFO - DEBUG: API key provided: Yes
2025-05-30 08:01:47,324 - luno_client - INFO - DEBUG: API secret provided: Yes
2025-05-30 08:01:47,324 - luno_client - INFO - DEBUG: API key length: 9
2025-05-30 08:01:47,324 - luno_client - INFO - DEBUG: API secret length: 12
2025-05-30 08:01:47,324 - luno_client - INFO - LunoAPIClient initialized successfully
2025-05-30 08:01:47,325 - luno_client - INFO - DEBUG: LunoAPIClient initialized
2025-05-30 08:01:47,325 - luno_client - INFO - DEBUG: API key provided: Yes
2025-05-30 08:01:47,325 - luno_client - INFO - DEBUG: API secret provided: Yes
2025-05-30 08:01:47,325 - luno_client - INFO - DEBUG: API key length: 13
2025-05-30 08:01:47,325 - luno_client - INFO - DEBUG: API secret length: 43
2025-05-30 08:01:47,325 - luno_client - INFO - LunoAPIClient initialized successfully
2025-05-30 08:01:47,325 - trading_bot - INFO - Trading Bot initialized
2025-05-30 08:01:47,325 - trading_bot - INFO - Dry run mode: True
2025-05-30 08:01:47,325 - trading_bot - INFO - Trading pair: XBTMYR
2025-05-30 08:01:56,585 - luno_client - INFO - DEBUG: LunoAPIClient initialized
2025-05-30 08:01:56,585 - luno_client - INFO - DEBUG: API key provided: Yes
2025-05-30 08:01:56,585 - luno_client - INFO - DEBUG: API secret provided: Yes
2025-05-30 08:01:56,585 - luno_client - INFO - DEBUG: API key length: 13
2025-05-30 08:01:56,585 - luno_client - INFO - DEBUG: API secret length: 43
2025-05-30 08:01:56,585 - luno_client - INFO - LunoAPIClient initialized successfully
2025-05-30 08:01:56,585 - trading_bot - INFO - Trading Bot initialized
2025-05-30 08:01:56,585 - trading_bot - INFO - Dry run mode: True
2025-05-30 08:01:56,585 - trading_bot - INFO - Trading pair: XBTMYR
2025-05-30 08:01:56,585 - trading_bot - INFO - Starting Trading Bot...
2025-05-30 08:01:56,585 - trading_bot - INFO - === Trading Bot Initial State ===
2025-05-30 08:01:56,585 - trading_bot - INFO - Config: XBTMYR
2025-05-30 08:01:56,585 - trading_bot - INFO - Max position size: 2.0%
2025-05-30 08:01:56,585 - trading_bot - INFO - Stop loss: 1.5%
2025-05-30 08:01:56,585 - trading_bot - INFO - Take profit: 3.0%
2025-05-30 08:01:56,870 - luno_client - INFO - INFO: Generating simulated candle data for XBTMYR (candles endpoint not available)
2025-05-30 08:01:57,075 - technical_analysis - INFO - Signal generated: SELL with 80.0% confidence
2025-05-30 08:01:57,075 - technical_analysis - INFO - Reasons: Price broke support, EMA bearish cross, MACD bearish, Volume confirmation, RSI healthy at 39.2
2025-05-30 08:01:57,075 - trading_bot - INFO - === Market State ===
2025-05-30 08:01:57,075 - trading_bot - INFO - Price: 451014.00
2025-05-30 08:01:57,075 - trading_bot - INFO - RSI: 39.2
2025-05-30 08:01:57,075 - trading_bot - INFO - EMA 9/21: 427607.5/431827.9
2025-05-30 08:01:57,076 - trading_bot - INFO - MACD: -4837.43
2025-05-30 08:01:57,076 - trading_bot - INFO - Sentiment: BEARISH
2025-05-30 08:01:57,076 - trading_bot - INFO - Signal: SELL (80.0%)
2025-05-30 08:01:57,076 - trading_bot - INFO - Reasons: Price broke support, EMA bearish cross, MACD bearish, Volume confirmation, RSI healthy at 39.2
2025-05-30 08:01:57,076 - trading_bot - WARNING - Calculated position size is zero
2025-05-30 08:02:07,278 - luno_client - INFO - INFO: Generating simulated candle data for XBTMYR (candles endpoint not available)
2025-05-30 08:02:07,480 - technical_analysis - INFO - Signal generated: WAIT with 20.0% confidence
2025-05-30 08:02:07,480 - technical_analysis - INFO - Reasons: Mixed signals
2025-05-30 08:02:07,480 - trading_bot - INFO - === Market State ===
2025-05-30 08:02:07,480 - trading_bot - INFO - Price: 451013.00
2025-05-30 08:02:07,480 - trading_bot - INFO - RSI: 74.4
2025-05-30 08:02:07,480 - trading_bot - INFO - EMA 9/21: 472912.2/467331.4
2025-05-30 08:02:07,480 - trading_bot - INFO - MACD: 5372.87
2025-05-30 08:02:07,480 - trading_bot - INFO - Sentiment: BULLISH
2025-05-30 08:02:07,481 - trading_bot - INFO - Signal: WAIT (20.0%)
2025-05-30 08:02:07,481 - trading_bot - INFO - Reasons: Mixed signals
2025-05-30 08:02:17,690 - luno_client - INFO - INFO: Generating simulated candle data for XBTMYR (candles endpoint not available)
2025-05-30 08:02:17,889 - technical_analysis - INFO - Signal generated: SELL with 80.0% confidence
2025-05-30 08:02:17,889 - technical_analysis - INFO - Reasons: Price broke support, EMA bearish cross, MACD bearish, Volume confirmation, RSI healthy at 61.64
2025-05-30 08:02:17,889 - trading_bot - INFO - === Market State ===
2025-05-30 08:02:17,889 - trading_bot - INFO - Price: 451014.00
2025-05-30 08:02:17,889 - trading_bot - INFO - RSI: 61.6
2025-05-30 08:02:17,889 - trading_bot - INFO - EMA 9/21: 441095.4/441193.8
2025-05-30 08:02:17,890 - trading_bot - INFO - MACD: -726.04
2025-05-30 08:02:17,890 - trading_bot - INFO - Sentiment: NEUTRAL
2025-05-30 08:02:17,890 - trading_bot - INFO - Signal: SELL (80.0%)
2025-05-30 08:02:17,890 - trading_bot - INFO - Reasons: Price broke support, EMA bearish cross, MACD bearish, Volume confirmation, RSI healthy at 61.64
2025-05-30 08:02:17,890 - trading_bot - WARNING - Calculated position size is zero
2025-05-30 08:02:28,696 - luno_client - INFO - INFO: Generating simulated candle data for XBTMYR (candles endpoint not available)
2025-05-30 08:02:28,941 - technical_analysis - INFO - Signal generated: WAIT with 20.0% confidence
2025-05-30 08:02:28,941 - technical_analysis - INFO - Reasons: RSI neutral
2025-05-30 08:02:28,941 - trading_bot - INFO - === Market State ===
2025-05-30 08:02:28,941 - trading_bot - INFO - Price: 451014.00
2025-05-30 08:02:28,941 - trading_bot - INFO - RSI: 41.7
2025-05-30 08:02:28,941 - trading_bot - INFO - EMA 9/21: 466869.0/465983.0
2025-05-30 08:02:28,941 - trading_bot - INFO - MACD: 2290.01
2025-05-30 08:02:28,941 - trading_bot - INFO - Sentiment: NEUTRAL
2025-05-30 08:02:28,941 - trading_bot - INFO - Signal: WAIT (20.0%)
2025-05-30 08:02:28,941 - trading_bot - INFO - Reasons: RSI neutral
2025-05-30 08:02:39,149 - luno_client - INFO - INFO: Generating simulated candle data for XBTMYR (candles endpoint not available)
2025-05-30 08:02:39,351 - technical_analysis - INFO - Signal generated: WAIT with 20.0% confidence
2025-05-30 08:02:39,351 - technical_analysis - INFO - Reasons: RSI neutral
2025-05-30 08:02:39,351 - trading_bot - INFO - === Market State ===
2025-05-30 08:02:39,351 - trading_bot - INFO - Price: 451014.00
2025-05-30 08:02:39,352 - trading_bot - INFO - RSI: 55.7
2025-05-30 08:02:39,352 - trading_bot - INFO - EMA 9/21: 449950.8/447544.5
2025-05-30 08:02:39,352 - trading_bot - INFO - MACD: 2521.29
2025-05-30 08:02:39,352 - trading_bot - INFO - Sentiment: BULLISH
2025-05-30 08:02:39,352 - trading_bot - INFO - Signal: WAIT (20.0%)
2025-05-30 08:02:39,352 - trading_bot - INFO - Reasons: RSI neutral
2025-05-30 08:02:49,553 - luno_client - INFO - INFO: Generating simulated candle data for XBTMYR (candles endpoint not available)
2025-05-30 08:02:49,756 - technical_analysis - INFO - Signal generated: SELL with 80.0% confidence
2025-05-30 08:02:49,756 - technical_analysis - INFO - Reasons: Price broke support, EMA bearish cross, MACD bearish, Volume confirmation, RSI healthy at 58.26
2025-05-30 08:02:49,756 - trading_bot - INFO - === Market State ===
2025-05-30 08:02:49,756 - trading_bot - INFO - Price: 451013.00
2025-05-30 08:02:49,756 - trading_bot - INFO - RSI: 58.3
2025-05-30 08:02:49,756 - trading_bot - INFO - EMA 9/21: 433623.3/434494.2
2025-05-30 08:02:49,756 - trading_bot - INFO - MACD: -2217.89
2025-05-30 08:02:49,756 - trading_bot - INFO - Sentiment: NEUTRAL
2025-05-30 08:02:49,756 - trading_bot - INFO - Signal: SELL (80.0%)
2025-05-30 08:02:49,756 - trading_bot - INFO - Reasons: Price broke support, EMA bearish cross, MACD bearish, Volume confirmation, RSI healthy at 58.26
2025-05-30 08:02:49,757 - trading_bot - WARNING - Calculated position size is zero
2025-05-30 08:02:59,974 - luno_client - INFO - INFO: Generating simulated candle data for XBTMYR (candles endpoint not available)
2025-05-30 08:03:00,317 - technical_analysis - INFO - Signal generated: WAIT with 20.0% confidence
2025-05-30 08:03:00,317 - technical_analysis - INFO - Reasons: Mixed signals
2025-05-30 08:03:00,317 - trading_bot - INFO - === Market State ===
2025-05-30 08:03:00,317 - trading_bot - INFO - Price: 451013.00
2025-05-30 08:03:00,318 - trading_bot - INFO - RSI: 32.7
2025-05-30 08:03:00,318 - trading_bot - INFO - EMA 9/21: 476753.9/476865.5
2025-05-30 08:03:00,318 - trading_bot - INFO - MACD: 1481.21
2025-05-30 08:03:00,318 - trading_bot - INFO - Sentiment: BEARISH
2025-05-30 08:03:00,318 - trading_bot - INFO - Signal: WAIT (20.0%)
2025-05-30 08:03:00,318 - trading_bot - INFO - Reasons: Mixed signals
2025-05-30 08:03:10,529 - luno_client - INFO - INFO: Generating simulated candle data for XBTMYR (candles endpoint not available)
2025-05-30 08:03:10,730 - technical_analysis - INFO - Signal generated: WAIT with 20.0% confidence
2025-05-30 08:03:10,730 - technical_analysis - INFO - Reasons: RSI neutral
2025-05-30 08:03:10,730 - trading_bot - INFO - === Market State ===
2025-05-30 08:03:10,730 - trading_bot - INFO - Price: 451012.00
2025-05-30 08:03:10,730 - trading_bot - INFO - RSI: 48.3
2025-05-30 08:03:10,730 - trading_bot - INFO - EMA 9/21: 459011.2/458707.3
2025-05-30 08:03:10,730 - trading_bot - INFO - MACD: 621.22
2025-05-30 08:03:10,730 - trading_bot - INFO - Sentiment: NEUTRAL
2025-05-30 08:03:10,730 - trading_bot - INFO - Signal: WAIT (20.0%)
2025-05-30 08:03:10,730 - trading_bot - INFO - Reasons: RSI neutral
2025-05-30 08:03:20,934 - luno_client - INFO - INFO: Generating simulated candle data for XBTMYR (candles endpoint not available)
2025-05-30 08:03:21,157 - technical_analysis - INFO - Signal generated: WAIT with 20.0% confidence
2025-05-30 08:03:21,157 - technical_analysis - INFO - Reasons: RSI neutral
2025-05-30 08:03:21,157 - trading_bot - INFO - === Market State ===
2025-05-30 08:03:21,157 - trading_bot - INFO - Price: 451012.00
2025-05-30 08:03:21,157 - trading_bot - INFO - RSI: 43.6
2025-05-30 08:03:21,158 - trading_bot - INFO - EMA 9/21: 455234.8/455017.3
2025-05-30 08:03:21,158 - trading_bot - INFO - MACD: 545.36
2025-05-30 08:03:21,158 - trading_bot - INFO - Sentiment: NEUTRAL
2025-05-30 08:03:21,158 - trading_bot - INFO - Signal: WAIT (20.0%)
2025-05-30 08:03:21,158 - trading_bot - INFO - Reasons: RSI neutral
2025-05-30 08:03:31,372 - luno_client - INFO - INFO: Generating simulated candle data for XBTMYR (candles endpoint not available)
2025-05-30 08:03:31,574 - technical_analysis - INFO - Signal generated: SELL with 80.0% confidence
2025-05-30 08:03:31,575 - technical_analysis - INFO - Reasons: Price broke support, EMA bearish cross, MACD bearish, Volume confirmation, RSI healthy at 44.05
2025-05-30 08:03:31,575 - trading_bot - INFO - === Market State ===
2025-05-30 08:03:31,575 - trading_bot - INFO - Price: 451012.00
2025-05-30 08:03:31,575 - trading_bot - INFO - RSI: 44.0
2025-05-30 08:03:31,575 - trading_bot - INFO - EMA 9/21: 429881.2/431907.3
2025-05-30 08:03:31,575 - trading_bot - INFO - MACD: -2814.19
2025-05-30 08:03:31,575 - trading_bot - INFO - Sentiment: NEUTRAL
2025-05-30 08:03:31,575 - trading_bot - INFO - Signal: SELL (80.0%)
2025-05-30 08:03:31,576 - trading_bot - INFO - Reasons: Price broke support, EMA bearish cross, MACD bearish, Volume confirmation, RSI healthy at 44.05
2025-05-30 08:03:31,576 - trading_bot - WARNING - Calculated position size is zero
2025-05-30 08:03:41,788 - luno_client - INFO - INFO: Generating simulated candle data for XBTMYR (candles endpoint not available)
2025-05-30 08:03:41,989 - technical_analysis - INFO - Signal generated: WAIT with 20.0% confidence
2025-05-30 08:03:41,989 - technical_analysis - INFO - Reasons: RSI neutral
2025-05-30 08:03:41,989 - trading_bot - INFO - === Market State ===
2025-05-30 08:03:41,990 - trading_bot - INFO - Price: 450999.00
2025-05-30 08:03:41,990 - trading_bot - INFO - RSI: 57.1
2025-05-30 08:03:41,990 - trading_bot - INFO - EMA 9/21: 448716.3/448636.1
2025-05-30 08:03:41,990 - trading_bot - INFO - MACD: -130.53
2025-05-30 08:03:41,990 - trading_bot - INFO - Sentiment: NEUTRAL
2025-05-30 08:03:41,990 - trading_bot - INFO - Signal: WAIT (20.0%)
2025-05-30 08:03:41,990 - trading_bot - INFO - Reasons: RSI neutral
2025-05-30 08:03:44,758 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:44,759 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:44,759 - trading_bot - INFO - Performance report saved: trading_report_20250530_080344.json
2025-05-30 08:03:44,759 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:45,738 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:45,739 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:45,740 - trading_bot - INFO - Performance report saved: trading_report_20250530_080345.json
2025-05-30 08:03:45,740 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:46,172 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:46,172 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:46,173 - trading_bot - INFO - Performance report saved: trading_report_20250530_080346.json
2025-05-30 08:03:46,173 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:46,388 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:46,388 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:46,388 - trading_bot - INFO - Performance report saved: trading_report_20250530_080346.json
2025-05-30 08:03:46,389 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:46,577 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:46,577 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:46,578 - trading_bot - INFO - Performance report saved: trading_report_20250530_080346.json
2025-05-30 08:03:46,578 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:46,765 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:46,765 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:46,766 - trading_bot - INFO - Performance report saved: trading_report_20250530_080346.json
2025-05-30 08:03:46,767 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:46,941 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:46,941 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:46,942 - trading_bot - INFO - Performance report saved: trading_report_20250530_080346.json
2025-05-30 08:03:46,943 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:47,133 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:47,133 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:47,135 - trading_bot - INFO - Performance report saved: trading_report_20250530_080347.json
2025-05-30 08:03:47,135 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:47,308 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:47,309 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:47,310 - trading_bot - INFO - Performance report saved: trading_report_20250530_080347.json
2025-05-30 08:03:47,310 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:47,468 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:47,468 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:47,469 - trading_bot - INFO - Performance report saved: trading_report_20250530_080347.json
2025-05-30 08:03:47,469 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:47,689 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:47,690 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:47,691 - trading_bot - INFO - Performance report saved: trading_report_20250530_080347.json
2025-05-30 08:03:47,691 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:48,867 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:48,867 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:48,868 - trading_bot - INFO - Performance report saved: trading_report_20250530_080348.json
2025-05-30 08:03:48,868 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:49,273 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:49,273 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:49,274 - trading_bot - INFO - Performance report saved: trading_report_20250530_080349.json
2025-05-30 08:03:49,274 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:49,501 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:49,501 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:49,502 - trading_bot - INFO - Performance report saved: trading_report_20250530_080349.json
2025-05-30 08:03:49,502 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:49,705 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:49,706 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:49,707 - trading_bot - INFO - Performance report saved: trading_report_20250530_080349.json
2025-05-30 08:03:49,707 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:49,921 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:49,921 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:49,923 - trading_bot - INFO - Performance report saved: trading_report_20250530_080349.json
2025-05-30 08:03:49,923 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:50,120 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:50,121 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:50,122 - trading_bot - INFO - Performance report saved: trading_report_20250530_080350.json
2025-05-30 08:03:50,122 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:50,341 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:50,342 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:50,343 - trading_bot - INFO - Performance report saved: trading_report_20250530_080350.json
2025-05-30 08:03:50,343 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:50,565 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:50,566 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:50,567 - trading_bot - INFO - Performance report saved: trading_report_20250530_080350.json
2025-05-30 08:03:50,567 - trading_bot - INFO - Trading Bot stopped
2025-05-30 08:03:50,834 - trading_bot - INFO - Received signal 2, shutting down gracefully...
2025-05-30 08:03:50,834 - trading_bot - INFO - Stopping Trading Bot...
2025-05-30 08:03:50,835 - trading_bot - INFO - Performance report saved: trading_report_20250530_080350.json
2025-05-30 08:03:50,835 - trading_bot - INFO - Trading Bot stopped
